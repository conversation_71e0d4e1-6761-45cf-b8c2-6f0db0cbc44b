# PDF Summarizer Installation Guide

This guide will help you install all the dependencies needed for the PDF summarizer with image and table extraction capabilities.

## 1. Python Dependencies

Run the `install_dependencies.bat` file to install the required Python packages:
- pdf2image
- pytesseract
- opencv-python
- camelot-py

Alternatively, you can run this command in your terminal:
```
pip install pdf2image pytesseract opencv-python camelot-py
```

## 2. System Dependencies

### 2.1 Tesseract OCR (for image text extraction)

1. Download the Tesseract installer for Windows from: https://github.com/UB-Mannheim/tesseract/wiki
2. Run the installer and follow the installation instructions
3. Make sure to check the option to add Tesseract to your PATH during installation
4. Default installation path is usually: `C:\Program Files\Tesseract-OCR`
5. After installation, verify it's working by opening a command prompt and typing:
   ```
   tesseract --version
   ```

### 2.2 Poppler (for PDF to image conversion)

1. Download Poppler for Windows from: https://github.com/oschwartz10612/poppler-windows/releases/
2. Extract the ZIP file to a location on your computer (e.g., `C:\Program Files\poppler`)
3. Add the `bin` directory to your PATH:
   - Right-click on "This PC" or "My Computer" and select "Properties"
   - Click on "Advanced system settings"
   - Click on "Environment Variables"
   - Under "System variables", find the "Path" variable, select it and click "Edit"
   - Click "New" and add the path to the `bin` directory (e.g., `C:\Program Files\poppler\bin`)
   - Click "OK" on all dialogs to save the changes

### 2.3 Ghostscript (for table extraction)

1. Download Ghostscript for Windows from: https://www.ghostscript.com/download/gsdnld.html
2. Run the installer and follow the installation instructions
3. Make sure to check the option to add Ghostscript to your PATH during installation
4. After installation, verify it's working by opening a command prompt and typing:
   ```
   gswin64c --version
   ```

## 3. Verifying Installation

To verify that all dependencies are installed correctly, run the CLI version of the PDF summarizer:

```
python cli_pdf_summarizer.py path/to/your/file.pdf
```

If everything is installed correctly, you should see output indicating that image and table extraction are available:

```
Image extraction dependencies are available!
Table extraction dependencies are available!

Available Features:
✓ Text Extraction
✓ Image Text Extraction
✓ Table Extraction
```

## Troubleshooting

### Tesseract OCR not found

If you get an error like "tesseract is not recognized as an internal or external command", make sure:
1. Tesseract is installed correctly
2. The Tesseract installation directory is in your PATH
3. You may need to restart your computer after adding to PATH

### Poppler not found

If you get an error related to pdf2image or poppler, make sure:
1. Poppler is extracted to a directory on your computer
2. The Poppler bin directory is in your PATH
3. You may need to restart your computer after adding to PATH

### Ghostscript not found

If you get an error related to camelot or ghostscript, make sure:
1. Ghostscript is installed correctly
2. The Ghostscript installation directory is in your PATH
3. You may need to restart your computer after adding to PATH
