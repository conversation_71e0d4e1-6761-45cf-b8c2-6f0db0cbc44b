This position paper advocates for the development of "socially aware language technologies," arguing that current advancements in Natural Language Processing (NLP), particularly with Large Language Models (LLMs), have neglected crucial social aspects of language.  The authors highlight the exacerbation of existing issues like bias, toxicity, and fairness concerns in LLMs, attributing these shortcomings to a lack of "social awareness"—the ability to understand social factors, contexts, and implications within which language operates.  The paper posits that integrating social awareness is crucial for creating more natural, helpful, and safe NLP applications.

The authors propose a framework for socially aware NLP encompassing three key aspects: social factors (speaker/receiver characteristics, social relations, context, norms, culture, communicative goals), social interaction (dynamics of human-AI and human-human interaction, power dynamics, trust, user expectations), and social implication (broad societal impact, including potential harms and benefits).  They draw upon theories from systemic functional linguistics and Gricean maxims to illustrate the social complexities often overlooked in current NLP approaches.  The paper further examines existing research efforts within this framework, categorizing them based on their focus on social factors, interaction, or implications.

Key findings and results are presented through a proposed taxonomy of social factors and a conceptual model illustrating the interconnectedness of social factors, interactions, and implications in the development of socially aware NLP. Table 1 summarizes six key research directions for socially aware NLP, highlighting the considerations (C1-C4: access to diverse communities, incorporation of context and interaction, ethical considerations, iterative design) needed for each.  The authors emphasize the need for new tasks, metrics, and evaluation methods tailored to assessing social awareness, contrasting it with existing NLP benchmarks that primarily focus on information-processing tasks.

The paper concludes by emphasizing the need for a unified subfield of "socially aware language technologies" to address the challenges of incorporating social intelligence into language models.  The authors stress the importance of a responsible development process that considers ethical implications, potential misuse, and the broader societal impact of these technologies.  They acknowledge limitations, particularly the lack of empirical evidence in the current paper, but propose numerous avenues for future research, including the development of novel evaluation methods and real-world applications of socially aware NLP.

The authors conclude by emphasizing that the future of NLP lies in addressing the “harder” problems of social awareness and emotional intelligence, aligning with a growing societal need for responsible and equitable AI.  They call for a paradigm shift in AI research, urging interdisciplinary collaboration to integrate models of emotions, values, and cultures into existing language models, thereby enabling more human-like and socially responsible interactions with AI systems.
