# Project Structure

This document describes the organization of the AI Agent for Summarizing Research Papers project.

## File Structure

```
ai-research-paper-summarizer/
├── main.py                    # Main application file
├── requirements.txt           # Python dependencies
├── README.md                 # Project documentation
├── INSTALLATION_GUIDE.md     # Detailed installation instructions
├── PROJECT_STRUCTURE.md      # This file
├── .gitignore               # Git ignore rules
├── run_app.bat              # Windows batch file to run the app (full mode)
├── run_app_basic.bat        # Windows batch file to run the app (basic mode)
├── sample_paper.pdf         # Sample PDF for testing
├── pdfs/                    # Directory for uploaded PDF files
└── summaries/               # Directory for generated summaries
```

## Key Files

### main.py
The main application file containing:
- Flask web server setup
- PDF text extraction functions
- Image and table extraction functions
- AI summarization using Google Gemini
- HTML templates for the web interface
- Command-line argument parsing

### requirements.txt
Lists all Python dependencies needed to run the application:
- flask (web framework)
- PyPDF2 (PDF text extraction)
- requests (API calls)
- pdf2image (PDF to image conversion)
- pytesseract (OCR for image text extraction)
- opencv-python (image processing)
- camelot-py (table extraction)
- numpy (numerical operations)

### README.md
Comprehensive documentation including:
- Project description
- Installation instructions
- Usage guide
- Troubleshooting tips

### INSTALLATION_GUIDE.md
Detailed step-by-step installation instructions for all system dependencies.

## Directories

### pdfs/
Stores uploaded PDF files temporarily. Files are automatically named with timestamps and UUIDs to prevent conflicts.

### summaries/
Stores generated text summaries. Each summary is saved as a .txt file with a corresponding timestamp and UUID.

## Batch Files

### run_app.bat
Runs the application in full mode with all features enabled (text, image, and table extraction).

### run_app_basic.bat
Runs the application in basic mode with only text extraction enabled. Use this if you encounter issues with Poppler or Ghostscript installation.

## Usage Modes

### Full Mode
```bash
python main.py
```
Enables all features including image and table extraction.

### Basic Mode
```bash
python main.py --basic-only
```
Uses only basic text extraction, skipping image and table processing.
